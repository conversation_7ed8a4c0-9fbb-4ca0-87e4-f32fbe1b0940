import { Outlet, redirect } from "@tanstack/react-router";

import { Header } from "~/components/layouts";

export const Route = createFileRoute({
  component: RouteComponent,
  beforeLoad: ({ context }) => {
    if (!context.isAuthenticated) {
      throw redirect({ to: "/login", search: { next: location.pathname } });
    }
  },
});

function RouteComponent() {
  return (
    <>
      <Header />

      <main className="app-container">
        <Outlet />
      </main>
    </>
  );
}
