/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

import type { CreateFileRoute, FileRoutesByPath } from '@tanstack/react-router'

import { Route as rootRouteImport } from './routes/__root'
import { Route as GuestRouteImport } from './routes/_guest'
import { Route as AuthRouteImport } from './routes/_auth'
import { Route as AuthIndexRouteImport } from './routes/_auth/index'
import { Route as GuestRegisterRouteImport } from './routes/_guest/register'
import { Route as GuestLoginRouteImport } from './routes/_guest/login'
import { Route as AuthTransactionsRouteImport } from './routes/_auth/transactions'
import { Route as AuthBudgetsRouteImport } from './routes/_auth/budgets'

const GuestRoute = GuestRouteImport.update({
  id: '/_guest',
  getParentRoute: () => rootRouteImport,
} as any)
const AuthRoute = AuthRouteImport.update({
  id: '/_auth',
  getParentRoute: () => rootRouteImport,
} as any)
const AuthIndexRoute = AuthIndexRouteImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => AuthRoute,
} as any)
const GuestRegisterRoute = GuestRegisterRouteImport.update({
  id: '/register',
  path: '/register',
  getParentRoute: () => GuestRoute,
} as any)
const GuestLoginRoute = GuestLoginRouteImport.update({
  id: '/login',
  path: '/login',
  getParentRoute: () => GuestRoute,
} as any)
const AuthTransactionsRoute = AuthTransactionsRouteImport.update({
  id: '/transactions',
  path: '/transactions',
  getParentRoute: () => AuthRoute,
} as any)
const AuthBudgetsRoute = AuthBudgetsRouteImport.update({
  id: '/budgets',
  path: '/budgets',
  getParentRoute: () => AuthRoute,
} as any)

export interface FileRoutesByFullPath {
  '': typeof GuestRouteWithChildren
  '/budgets': typeof AuthBudgetsRoute
  '/transactions': typeof AuthTransactionsRoute
  '/login': typeof GuestLoginRoute
  '/register': typeof GuestRegisterRoute
  '/': typeof AuthIndexRoute
}
export interface FileRoutesByTo {
  '': typeof GuestRouteWithChildren
  '/budgets': typeof AuthBudgetsRoute
  '/transactions': typeof AuthTransactionsRoute
  '/login': typeof GuestLoginRoute
  '/register': typeof GuestRegisterRoute
  '/': typeof AuthIndexRoute
}
export interface FileRoutesById {
  __root__: typeof rootRouteImport
  '/_auth': typeof AuthRouteWithChildren
  '/_guest': typeof GuestRouteWithChildren
  '/_auth/budgets': typeof AuthBudgetsRoute
  '/_auth/transactions': typeof AuthTransactionsRoute
  '/_guest/login': typeof GuestLoginRoute
  '/_guest/register': typeof GuestRegisterRoute
  '/_auth/': typeof AuthIndexRoute
}
export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths: '' | '/budgets' | '/transactions' | '/login' | '/register' | '/'
  fileRoutesByTo: FileRoutesByTo
  to: '' | '/budgets' | '/transactions' | '/login' | '/register' | '/'
  id:
    | '__root__'
    | '/_auth'
    | '/_guest'
    | '/_auth/budgets'
    | '/_auth/transactions'
    | '/_guest/login'
    | '/_guest/register'
    | '/_auth/'
  fileRoutesById: FileRoutesById
}
export interface RootRouteChildren {
  AuthRoute: typeof AuthRouteWithChildren
  GuestRoute: typeof GuestRouteWithChildren
}

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/_auth': {
      id: '/_auth'
      path: ''
      fullPath: ''
      preLoaderRoute: typeof AuthRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/_guest': {
      id: '/_guest'
      path: ''
      fullPath: ''
      preLoaderRoute: typeof GuestRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/_auth/budgets': {
      id: '/_auth/budgets'
      path: '/budgets'
      fullPath: '/budgets'
      preLoaderRoute: typeof AuthBudgetsRouteImport
      parentRoute: typeof AuthRoute
    }
    '/_auth/transactions': {
      id: '/_auth/transactions'
      path: '/transactions'
      fullPath: '/transactions'
      preLoaderRoute: typeof AuthTransactionsRouteImport
      parentRoute: typeof AuthRoute
    }
    '/_guest/login': {
      id: '/_guest/login'
      path: '/login'
      fullPath: '/login'
      preLoaderRoute: typeof GuestLoginRouteImport
      parentRoute: typeof GuestRoute
    }
    '/_guest/register': {
      id: '/_guest/register'
      path: '/register'
      fullPath: '/register'
      preLoaderRoute: typeof GuestRegisterRouteImport
      parentRoute: typeof GuestRoute
    }
    '/_auth/': {
      id: '/_auth/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof AuthIndexRouteImport
      parentRoute: typeof AuthRoute
    }
  }
}

declare module './routes/_auth' {
  const createFileRoute: CreateFileRoute<
    '/_auth',
    FileRoutesByPath['/_auth']['parentRoute'],
    FileRoutesByPath['/_auth']['id'],
    FileRoutesByPath['/_auth']['path'],
    FileRoutesByPath['/_auth']['fullPath']
  >
}
declare module './routes/_guest' {
  const createFileRoute: CreateFileRoute<
    '/_guest',
    FileRoutesByPath['/_guest']['parentRoute'],
    FileRoutesByPath['/_guest']['id'],
    FileRoutesByPath['/_guest']['path'],
    FileRoutesByPath['/_guest']['fullPath']
  >
}
declare module './routes/_auth/budgets' {
  const createFileRoute: CreateFileRoute<
    '/_auth/budgets',
    FileRoutesByPath['/_auth/budgets']['parentRoute'],
    FileRoutesByPath['/_auth/budgets']['id'],
    FileRoutesByPath['/_auth/budgets']['path'],
    FileRoutesByPath['/_auth/budgets']['fullPath']
  >
}
declare module './routes/_auth/transactions' {
  const createFileRoute: CreateFileRoute<
    '/_auth/transactions',
    FileRoutesByPath['/_auth/transactions']['parentRoute'],
    FileRoutesByPath['/_auth/transactions']['id'],
    FileRoutesByPath['/_auth/transactions']['path'],
    FileRoutesByPath['/_auth/transactions']['fullPath']
  >
}
declare module './routes/_guest/login' {
  const createFileRoute: CreateFileRoute<
    '/_guest/login',
    FileRoutesByPath['/_guest/login']['parentRoute'],
    FileRoutesByPath['/_guest/login']['id'],
    FileRoutesByPath['/_guest/login']['path'],
    FileRoutesByPath['/_guest/login']['fullPath']
  >
}
declare module './routes/_guest/register' {
  const createFileRoute: CreateFileRoute<
    '/_guest/register',
    FileRoutesByPath['/_guest/register']['parentRoute'],
    FileRoutesByPath['/_guest/register']['id'],
    FileRoutesByPath['/_guest/register']['path'],
    FileRoutesByPath['/_guest/register']['fullPath']
  >
}
declare module './routes/_auth/index' {
  const createFileRoute: CreateFileRoute<
    '/_auth/',
    FileRoutesByPath['/_auth/']['parentRoute'],
    FileRoutesByPath['/_auth/']['id'],
    FileRoutesByPath['/_auth/']['path'],
    FileRoutesByPath['/_auth/']['fullPath']
  >
}

interface AuthRouteChildren {
  AuthBudgetsRoute: typeof AuthBudgetsRoute
  AuthTransactionsRoute: typeof AuthTransactionsRoute
  AuthIndexRoute: typeof AuthIndexRoute
}

const AuthRouteChildren: AuthRouteChildren = {
  AuthBudgetsRoute: AuthBudgetsRoute,
  AuthTransactionsRoute: AuthTransactionsRoute,
  AuthIndexRoute: AuthIndexRoute,
}

const AuthRouteWithChildren = AuthRoute._addFileChildren(AuthRouteChildren)

interface GuestRouteChildren {
  GuestLoginRoute: typeof GuestLoginRoute
  GuestRegisterRoute: typeof GuestRegisterRoute
}

const GuestRouteChildren: GuestRouteChildren = {
  GuestLoginRoute: GuestLoginRoute,
  GuestRegisterRoute: GuestRegisterRoute,
}

const GuestRouteWithChildren = GuestRoute._addFileChildren(GuestRouteChildren)

const rootRouteChildren: RootRouteChildren = {
  AuthRoute: AuthRouteWithChildren,
  GuestRoute: GuestRouteWithChildren,
}
export const routeTree = rootRouteImport
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()
