import { useEffect } from "react";

import { RouterProvider } from "@tanstack/react-router";
import { LoaderIcon } from "lucide-react";

import { Toaster } from "./components/ui/sonner";
import { useLoadAuth } from "./features/auth/hooks";
import useAuthStore from "./features/auth/store";
import router from "./router";

export default function App() {
  const isAuthenticated = useAuthStore((state) => state.isAuthenticated);

  const { isLoading, loadAuth } = useLoadAuth();

  useEffect(() => {
    void loadAuth();
  }, [loadAuth]);

  if (isLoading) {
    return (
      <div className="flex h-dvh items-center justify-center">
        <LoaderIcon className="h-16 animate-spin" />
      </div>
    );
  }

  return (
    <>
      <RouterProvider router={router} context={{ isAuthenticated }} />
      <Toaster />
    </>
  );
}
