# 💳 Accounts Feature Request

## 📋 Overview

This feature request outlines the implementation of a comprehensive accounts management system for Finanze.Pro. The feature will enable users to manage their financial accounts and organize them into groups for better categorization and overview.

## 🎯 Goals

- Provide users with a centralized view of all their financial accounts
- Enable account grouping for better organization (e.g., "Cash", "Bank Accounts", "Credit Cards")
- Implement full CRUD operations for both accounts and account groups
- Ensure seamless integration with existing transaction and budget features

## 🔧 Technical Requirements

### API Integration
The feature will utilize existing API endpoints:

**Accounts:**
- `GET /api/v1/accounts` - List all accounts
- `GET /api/v1/accounts/{id}` - Get account details
- `POST /api/v1/accounts` - Create new account
- `PUT /api/v1/accounts/{id}` - Update account
- `DELETE /api/v1/accounts/{id}` - Delete account
- `PUT /api/v1/accounts/{id}/balance` - Update account balance

**Account Groups:**
- `GET /api/v1/account-groups` - List all account groups
- `GET /api/v1/account-groups/{id}` - Get account group details
- `POST /api/v1/account-groups` - Create new account group
- `PUT /api/v1/account-groups/{id}` - Update account group
- `DELETE /api/v1/account-groups/{id}` - Delete account group

### Data Models

**Account:**
```typescript
{
  id: string;
  groupId: string | null;
  group?: AccountGroup;
  name: string;
  currency: Currency;
  type: AccountType; // "cash" | "card" | "bank_account" | "savings" | "loan" | "other"
  description: string | null;
  color: string | null;
  overdraftLimit: Decimal;
  isActive: boolean;
  openingBalance: Decimal;
  currentBalance: Decimal;
  baseOpeningBalance: Decimal;
  baseCurrentBalance: Decimal;
  createdAt: string;
  updatedAt: string;
}
```

**Account Group:**
```typescript
{
  id: string;
  name: string;
  color: string | null;
  iconUrl: string | null;
  createdAt: string;
  updatedAt: string;
}
```

## 🖥️ User Interface

### 1. Accounts List Page (`/accounts`)

**Layout:**
- Page header with title "💳 Accounts" and "Add Account" button
- Account groups displayed as expandable sections
- Ungrouped accounts shown in a separate section
- Each account shows: name, type, current balance, currency
- Action buttons for each account: Edit, Delete
- Action buttons for each group: Edit, Delete, Add Account to Group

**Features:**
- Search/filter accounts by name or type
- Toggle between grouped and flat view
- Quick balance overview (total across all accounts)
- Visual indicators for account types (icons)
- Color coding for accounts and groups

### 2. Account Details Page (`/accounts/{id}`)

**Layout:**
- Account header with name, type, and current balance
- Account information card (currency, description, overdraft limit, etc.)
- Recent transactions list (integration with transactions feature)
- Quick actions: Edit Account, Update Balance, View All Transactions

**Features:**
- Balance history chart (if available)
- Account status indicator (active/inactive)
- Group membership display with option to change

### 3. Dialog Components

#### Account Creation Dialog
**Form Fields:**
- Account name (required)
- Account type (dropdown: Cash, Card, Bank Account, Savings, Loan, Other)
- Currency (dropdown with search)
- Account group (optional dropdown)
- Description (optional textarea)
- Color picker (optional)
- Opening balance (required, decimal input)
- Overdraft limit (optional, decimal input)

#### Account Edit Dialog
**Form Fields:**
- Same as creation form, but with current values pre-filled
- Additional field: Active status (toggle)
- Cannot change opening balance (read-only)

#### Account Group Dialog (Create/Edit)
**Form Fields:**
- Group name (required)
- Color picker (optional)
- Icon URL (optional, with preview)

## 🔄 User Workflows

### Creating an Account
1. User clicks "Add Account" button on accounts page
2. Account creation dialog opens
3. User fills required fields (name, type, currency, opening balance)
4. User optionally selects group, adds description, chooses color
5. User submits form
6. Account is created and user is redirected to accounts list
7. Success toast notification is shown

### Managing Account Groups
1. User clicks "Add Group" button or "Edit" on existing group
2. Account group dialog opens
3. User enters/edits group name and optional styling
4. User submits form
5. Group is created/updated
6. Accounts list refreshes to show changes
7. Success toast notification is shown

### Editing Account Details
1. User clicks "Edit" button on account card or details page
2. Account edit dialog opens with pre-filled values
3. User modifies desired fields
4. User submits form
5. Account is updated
6. UI refreshes to show changes
7. Success toast notification is shown

## 🎨 Design Considerations

### Visual Hierarchy
- Account groups as collapsible sections with clear headers
- Account cards with consistent layout and visual cues
- Color coding for quick identification
- Icons for different account types

### Responsive Design
- Mobile-first approach
- Collapsible sidebar for filters on mobile
- Touch-friendly buttons and interactions
- Optimized card layouts for different screen sizes

### Accessibility
- Proper ARIA labels and roles
- Keyboard navigation support
- Screen reader friendly content
- High contrast color options

## 🔗 Integration Points

### Navigation
- Add "Accounts" link to main navigation header
- Update dashboard to show account summary widgets

### Transactions Feature
- Account selector in transaction forms
- Account balance updates when transactions are created/modified
- Deep linking from account details to filtered transactions

### Budgets Feature
- Account-based budget categories
- Multi-account budget tracking

## 📱 Implementation Phases

### Phase 1: Core Functionality
- Accounts list page with basic CRUD operations
- Account and account group dialogs
- Basic account details page

### Phase 2: Enhanced Features
- Advanced filtering and search
- Account grouping and organization
- Balance update functionality

### Phase 3: Integration & Polish
- Transaction integration
- Dashboard widgets
- Performance optimizations
- Enhanced mobile experience

## ✅ Acceptance Criteria

- [ ] Users can view all accounts in a organized list
- [ ] Users can create, edit, and delete accounts
- [ ] Users can create, edit, and delete account groups
- [ ] Users can organize accounts into groups
- [ ] Users can view detailed account information
- [ ] All forms include proper validation and error handling
- [ ] UI is responsive and accessible
- [ ] Integration with existing navigation and routing
- [ ] Proper loading states and error handling
- [ ] Success/error notifications for all operations

## 🚀 Future Enhancements

- Account balance history and charts
- Account reconciliation features
- Import/export account data
- Account templates for quick setup
- Advanced reporting and analytics
- Multi-currency account support enhancements
